//
//  SettingViewController.m
//  photoclear
//
//  Created by lifubing on 2025/9/23.
//

#import "SettingViewController.h"
#import "MembershipPurchaseViewController.h"
#import "PhotoTools.h"
#import "Preferences.h"
#import <Photos/Photos.h>
#import "photoclear-Swift.h"

@interface SettingViewController ()

@end

@implementation SettingViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor blackColor];
    self.title = @"设置";

    [self setupMembershipCard];
    [self setupStatsView];
    [self updateStatsData];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self updateStatsData];
}

#pragma mark - Setup Stats View

- (void)setupStatsView {
    // 创建标题标签
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = @"数据统计";
    self.titleLabel.textColor = [UIColor whiteColor];
    self.titleLabel.font = [UIFont boldSystemFontOfSize:18];
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.titleLabel];
    
    // 创建主统计卡片
    self.mainStatsCard = [[UIView alloc] init];
    self.mainStatsCard.backgroundColor = [UIColor colorWithRed:0.15 green:0.15 blue:0.15 alpha:1.0];
    self.mainStatsCard.layer.cornerRadius = 8;
    self.mainStatsCard.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.mainStatsCard];
    
    // 创建统计行容器
    self.statsRowView = [[UIView alloc] init];
    self.statsRowView.backgroundColor = [UIColor clearColor];
    self.statsRowView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.mainStatsCard addSubview:self.statsRowView];
    
    // 创建三个统计卡片
    self.totalPhotosCard = [self createStatsCardWithTitle:@"照片" count:@"0"];
    self.deletedPhotosCard = [self createStatsCardWithTitle:@"已删除" count:@"0"];
    self.processedPhotosCard = [self createStatsCardWithTitle:@"已处理" count:@"0"];
    
    [self.statsRowView addSubview:self.totalPhotosCard];
    [self.statsRowView addSubview:self.deletedPhotosCard];
    [self.statsRowView addSubview:self.processedPhotosCard];
    
    // 创建进度条容器
    self.progressContainer = [[UIView alloc] init];
    self.progressContainer.backgroundColor = [UIColor clearColor];
    self.progressContainer.translatesAutoresizingMaskIntoConstraints = NO;
    [self.mainStatsCard addSubview:self.progressContainer];
    
    // 创建进度标签
    self.progressLabel = [[UILabel alloc] init];
    self.progressLabel.text = @"处理进度";
    self.progressLabel.textColor = [UIColor lightGrayColor];
    self.progressLabel.font = [UIFont systemFontOfSize:14];
    self.progressLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.progressContainer addSubview:self.progressLabel];
    
    // 创建百分比标签（紧跟在"处理进度"后面）
    self.progressPercentLabel = [[UILabel alloc] init];
    self.progressPercentLabel.text = @"0.0%";
    self.progressPercentLabel.textColor = [UIColor whiteColor];
    self.progressPercentLabel.font = [UIFont boldSystemFontOfSize:14];
    self.progressPercentLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.progressContainer addSubview:self.progressPercentLabel];
    
    // 创建照片总数标签（右侧显示）
    self.totalCountLabel = [[UILabel alloc] init];
    self.totalCountLabel.text = @"0 总数";
    self.totalCountLabel.textColor = [UIColor lightGrayColor];
    self.totalCountLabel.font = [UIFont systemFontOfSize:14];
    self.totalCountLabel.textAlignment = NSTextAlignmentRight;
    self.totalCountLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.progressContainer addSubview:self.totalCountLabel];
    
    // 创建进度条背景
    self.progressBarBackground = [[UIView alloc] init];
    self.progressBarBackground.backgroundColor = [UIColor colorWithRed:0.3 green:0.3 blue:0.3 alpha:1.0];
    self.progressBarBackground.layer.cornerRadius = 2;
    self.progressBarBackground.translatesAutoresizingMaskIntoConstraints = NO;
    [self.progressContainer addSubview:self.progressBarBackground];
    
    // 创建进度条填充
    self.progressBarFill = [[UIView alloc] init];
    self.progressBarFill.backgroundColor = [UIColor systemBlueColor];
    self.progressBarFill.layer.cornerRadius = 2;
    self.progressBarFill.translatesAutoresizingMaskIntoConstraints = NO;
    [self.progressBarBackground addSubview:self.progressBarFill];
    
    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        // 标题标签约束
        [self.titleLabel.topAnchor constraintEqualToAnchor:self.membershipCard.bottomAnchor constant:20],
        [self.titleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:20],
        [self.titleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-20],
        
        // 主统计卡片约束
        [self.mainStatsCard.topAnchor constraintEqualToAnchor:self.titleLabel.bottomAnchor constant:15],
        [self.mainStatsCard.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:20],
        [self.mainStatsCard.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-20],
        
        // 统计行容器约束
        [self.statsRowView.topAnchor constraintEqualToAnchor:self.mainStatsCard.topAnchor constant:20],
        [self.statsRowView.leadingAnchor constraintEqualToAnchor:self.mainStatsCard.leadingAnchor constant:20],
        [self.statsRowView.trailingAnchor constraintEqualToAnchor:self.mainStatsCard.trailingAnchor constant:-20],
        [self.statsRowView.heightAnchor constraintEqualToConstant:68],
        
        // 第一个卡片约束
        [self.totalPhotosCard.leadingAnchor constraintEqualToAnchor:self.statsRowView.leadingAnchor],
        [self.totalPhotosCard.topAnchor constraintEqualToAnchor:self.statsRowView.topAnchor],
        [self.totalPhotosCard.bottomAnchor constraintEqualToAnchor:self.statsRowView.bottomAnchor],
        
        // 第二个卡片约束
        [self.deletedPhotosCard.leadingAnchor constraintEqualToAnchor:self.totalPhotosCard.trailingAnchor constant:10],
        [self.deletedPhotosCard.topAnchor constraintEqualToAnchor:self.statsRowView.topAnchor],
        [self.deletedPhotosCard.bottomAnchor constraintEqualToAnchor:self.statsRowView.bottomAnchor],
        [self.deletedPhotosCard.widthAnchor constraintEqualToAnchor:self.totalPhotosCard.widthAnchor],
        
        // 第三个卡片约束
        [self.processedPhotosCard.leadingAnchor constraintEqualToAnchor:self.deletedPhotosCard.trailingAnchor constant:10],
        [self.processedPhotosCard.trailingAnchor constraintEqualToAnchor:self.statsRowView.trailingAnchor],
        [self.processedPhotosCard.topAnchor constraintEqualToAnchor:self.statsRowView.topAnchor],
        [self.processedPhotosCard.bottomAnchor constraintEqualToAnchor:self.statsRowView.bottomAnchor],
        [self.processedPhotosCard.widthAnchor constraintEqualToAnchor:self.totalPhotosCard.widthAnchor],
        
        // 进度容器约束
        [self.progressContainer.topAnchor constraintEqualToAnchor:self.statsRowView.bottomAnchor constant:8],
        [self.progressContainer.leadingAnchor constraintEqualToAnchor:self.mainStatsCard.leadingAnchor constant:20],
        [self.progressContainer.trailingAnchor constraintEqualToAnchor:self.mainStatsCard.trailingAnchor constant:-20],
        [self.progressContainer.bottomAnchor constraintEqualToAnchor:self.mainStatsCard.bottomAnchor constant:-12],
        
        // 进度标签约束
        [self.progressLabel.topAnchor constraintEqualToAnchor:self.progressContainer.topAnchor],
        [self.progressLabel.leadingAnchor constraintEqualToAnchor:self.progressContainer.leadingAnchor],
        [self.progressLabel.heightAnchor constraintEqualToConstant:24],

        // 百分比标签约束（紧跟在进度标签后面）
        [self.progressPercentLabel.centerYAnchor constraintEqualToAnchor:self.progressLabel.centerYAnchor],
        [self.progressPercentLabel.leadingAnchor constraintEqualToAnchor:self.progressLabel.trailingAnchor constant:8],
        
        // 照片总数标签约束（右侧显示）
        [self.totalCountLabel.centerYAnchor constraintEqualToAnchor:self.progressLabel.centerYAnchor],
        [self.totalCountLabel.trailingAnchor constraintEqualToAnchor:self.progressContainer.trailingAnchor],
        [self.totalCountLabel.leadingAnchor constraintGreaterThanOrEqualToAnchor:self.progressPercentLabel.trailingAnchor constant:10],
        
        // 进度条背景约束
        [self.progressBarBackground.topAnchor constraintEqualToAnchor:self.progressLabel.bottomAnchor constant:10],
        [self.progressBarBackground.leadingAnchor constraintEqualToAnchor:self.progressContainer.leadingAnchor],
        [self.progressBarBackground.trailingAnchor constraintEqualToAnchor:self.progressContainer.trailingAnchor],
        [self.progressBarBackground.heightAnchor constraintEqualToConstant:8],
        [self.progressBarBackground.bottomAnchor constraintEqualToAnchor:self.progressContainer.bottomAnchor],
        
        // 进度条填充约束
        [self.progressBarFill.topAnchor constraintEqualToAnchor:self.progressBarBackground.topAnchor],
        [self.progressBarFill.leadingAnchor constraintEqualToAnchor:self.progressBarBackground.leadingAnchor],
        [self.progressBarFill.bottomAnchor constraintEqualToAnchor:self.progressBarBackground.bottomAnchor],
    ]];
    
    // 保存进度条宽度约束
    self.progressBarWidthConstraint = [self.progressBarFill.widthAnchor constraintEqualToConstant:0];
    self.progressBarWidthConstraint.active = YES;
}

- (UIView *)createStatsCardWithTitle:(NSString *)title count:(NSString *)count {
    UIView *cardView = [[UIView alloc] init];
    cardView.backgroundColor = [UIColor colorWithRed:0.25 green:0.25 blue:0.25 alpha:1.0];
    cardView.layer.cornerRadius = 2;
    cardView.translatesAutoresizingMaskIntoConstraints = NO;
    
    // 创建数量标签
    UILabel *countLabel = [[UILabel alloc] init];
    countLabel.text = count;
    countLabel.textColor = [UIColor whiteColor];
    countLabel.font = [UIFont boldSystemFontOfSize:20];
    countLabel.textAlignment = NSTextAlignmentCenter;
    countLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [cardView addSubview:countLabel];
    
    // 创建标题标签
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = title;
    titleLabel.textColor = [UIColor lightGrayColor];
    titleLabel.font = [UIFont systemFontOfSize:12];
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [cardView addSubview:titleLabel];
    
    // 保存标签引用
    if ([title isEqualToString:@"照片"]) {
        self.totalPhotosLabel = titleLabel;
        self.totalPhotosCountLabel = countLabel;
    } else if ([title isEqualToString:@"已删除"]) {
        self.deletedPhotosLabel = titleLabel;
        self.deletedPhotosCountLabel = countLabel;
    } else if ([title isEqualToString:@"已处理"]) {
        self.processedPhotosLabel = titleLabel;
        self.processedPhotosCountLabel = countLabel;
    }
    
    // 设置标签约束
    [NSLayoutConstraint activateConstraints:@[
        // 数量标签约束
        [countLabel.centerXAnchor constraintEqualToAnchor:cardView.centerXAnchor],
        [countLabel.centerYAnchor constraintEqualToAnchor:cardView.centerYAnchor constant:-8],
        
        // 标题标签约束
        [titleLabel.centerXAnchor constraintEqualToAnchor:cardView.centerXAnchor],
        [titleLabel.topAnchor constraintEqualToAnchor:countLabel.bottomAnchor constant:4],
        [titleLabel.leadingAnchor constraintEqualToAnchor:cardView.leadingAnchor constant:5],
        [titleLabel.trailingAnchor constraintEqualToAnchor:cardView.trailingAnchor constant:-5]
    ]];
    
    return cardView;
}

#pragma mark - Setup Membership Card

- (void)setupMembershipCard {
    // 创建会员卡片容器
    self.membershipCard = [[UIView alloc] init];
    self.membershipCard.backgroundColor = [UIColor colorWithRed:1.0 green:0.8 blue:0.4 alpha:1.0]; // 金黄色背景
    self.membershipCard.layer.cornerRadius = 12;
    self.membershipCard.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.membershipCard];

    // 创建皇冠图标
    self.crownIconView = [[UIImageView alloc] init];
    self.crownIconView.image = [UIImage systemImageNamed:@"crown.fill"];
    self.crownIconView.tintColor = [UIColor colorWithRed:1.0 green:0.6 blue:0.0 alpha:1.0]; // 橙色
    self.crownIconView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.membershipCard addSubview:self.crownIconView];

    // 创建标题标签
    self.membershipTitleLabel = [[UILabel alloc] init];
    self.membershipTitleLabel.text = @"升级高级VIP";
    self.membershipTitleLabel.textColor = [UIColor colorWithRed:0.6 green:0.3 blue:0.0 alpha:1.0]; // 深橙色
    self.membershipTitleLabel.font = [UIFont boldSystemFontOfSize:15];
    self.membershipTitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.membershipCard addSubview:self.membershipTitleLabel];

    // 创建描述标签
    self.membershipDescriptionLabel = [[UILabel alloc] init];
    self.membershipDescriptionLabel.text = @"免费用户只能删除50张照片，升级会员解锁无限删除";
    self.membershipDescriptionLabel.textColor = [UIColor colorWithRed:0.5 green:0.25 blue:0.0 alpha:1.0]; // 深棕色
    self.membershipDescriptionLabel.font = [UIFont systemFontOfSize:12];
    self.membershipDescriptionLabel.numberOfLines = 2;
    self.membershipDescriptionLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.membershipCard addSubview:self.membershipDescriptionLabel];

    // 创建购买按钮
    self.purchaseButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.purchaseButton setTitle:@"限时特惠" forState:UIControlStateNormal];
    [self.purchaseButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.purchaseButton.titleLabel.font = [UIFont boldSystemFontOfSize:12];
    self.purchaseButton.backgroundColor = [UIColor colorWithRed:1.0 green:0.6 blue:0.0 alpha:1.0]; // 橙色背景
    self.purchaseButton.layer.cornerRadius = 16;
    self.purchaseButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.purchaseButton addTarget:self action:@selector(purchaseButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [self.membershipCard addSubview:self.purchaseButton];

    // 设置约束
    
    CGFloat membershipCardHeight = 72;
    if ([Preferences sharedInstance].didVipGet) {
        membershipCardHeight = 56;
    }
    [NSLayoutConstraint activateConstraints:@[
        // 会员卡片约束
        [self.membershipCard.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor constant:20],
        [self.membershipCard.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:20],
        [self.membershipCard.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-20],
        [self.membershipCard.heightAnchor constraintEqualToConstant:membershipCardHeight],

        // 皇冠图标约束
        [self.crownIconView.leadingAnchor constraintEqualToAnchor:self.membershipCard.leadingAnchor constant:12],
        [self.crownIconView.centerYAnchor constraintEqualToAnchor:self.membershipCard.centerYAnchor],
        [self.crownIconView.widthAnchor constraintEqualToConstant:28],
        [self.crownIconView.heightAnchor constraintEqualToConstant:28],

        // 标题标签约束
        [self.membershipTitleLabel.leadingAnchor constraintEqualToAnchor:self.crownIconView.trailingAnchor constant:12],
        [self.membershipTitleLabel.topAnchor constraintEqualToAnchor:self.membershipCard.topAnchor constant:8],
        [self.membershipTitleLabel.trailingAnchor constraintLessThanOrEqualToAnchor:self.purchaseButton.leadingAnchor constant:-10],

        // 描述标签约束
        [self.membershipDescriptionLabel.leadingAnchor constraintEqualToAnchor:self.membershipTitleLabel.leadingAnchor],
        [self.membershipDescriptionLabel.topAnchor constraintEqualToAnchor:self.membershipTitleLabel.bottomAnchor constant:2],
        [self.membershipDescriptionLabel.trailingAnchor constraintLessThanOrEqualToAnchor:self.purchaseButton.leadingAnchor constant:-10],

        // 购买按钮约束
        [self.purchaseButton.trailingAnchor constraintEqualToAnchor:self.membershipCard.trailingAnchor constant:-12],
        [self.purchaseButton.centerYAnchor constraintEqualToAnchor:self.membershipCard.centerYAnchor],
        [self.purchaseButton.widthAnchor constraintEqualToConstant:68],
        [self.purchaseButton.heightAnchor constraintEqualToConstant:32]
    ]];

    // 根据会员状态更新卡片显示
    [self updateMembershipCardVisibility];
}

- (void)updateMembershipCardVisibility {
    BOOL isVip = [Preferences sharedInstance].didVipGet;
    NSInteger deletedCount = [Preferences sharedInstance].deleteDataCount;

    // 如果已经是会员或者删除数量少于45张，隐藏会员卡片
    if (isVip) {
        self.membershipDescriptionLabel.text = @"您已解锁无限删除额度。";
        self.membershipTitleLabel.text = @"已开通高级VIP";
    } else {
        // 更新描述文字显示剩余额度
        self.membershipTitleLabel.text = @"升级高级VIP";
        NSInteger remainingCount = 50 - deletedCount;
        if (remainingCount <= 0) {
            self.membershipDescriptionLabel.text = @"免费额度已用完，升级会员解锁无限删除。";
        } else {
            self.membershipDescriptionLabel.text = [NSString stringWithFormat:@"免费用户只能删除50张照片，升级会员解除限制。剩余额度%ld张。", (long)remainingCount];
        }
    }
}

- (void)purchaseButtonTapped:(UIButton *)sender {
    // 创建并跳转到会员购买页面
    MembershipPurchaseViewController *membershipVC = [[MembershipPurchaseViewController alloc] init];
    [self.navigationController pushViewController:membershipVC animated:YES];
}

#pragma mark - Update Stats Data

- (void)updateStatsData {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        // 获取照片总数
        NSInteger totalPhotosCount = 0;
        PHAuthorizationStatus status = [PHPhotoLibrary authorizationStatus];
        if (status == PHAuthorizationStatusAuthorized) {
            PHFetchResult<PHAsset *> *allPhotos = [PhotoTools allPhotos];
            totalPhotosCount = allPhotos.count;
        }
        
        // 获取已删除照片数
        NSInteger deletedPhotosCount = [Preferences sharedInstance].deleteDataCount;
        
        // 获取已处理照片数
        NSInteger processedPhotosCount = [Preferences sharedInstance].deleteDataCount + [Preferences sharedInstance].archiveDataCount;
        
        // 计算处理进度百分比
        CGFloat progressPercent = 0.0;
        if (totalPhotosCount > 0) {
            progressPercent = (CGFloat)processedPhotosCount / (CGFloat)totalPhotosCount * 100.0;
        }
        
        // 在主线程更新UI
        dispatch_async(dispatch_get_main_queue(), ^{
            // 更新数字
            self.totalPhotosCountLabel.text = [NSString stringWithFormat:@"%ld", (long)totalPhotosCount];
            self.deletedPhotosCountLabel.text = [NSString stringWithFormat:@"%ld", (long)deletedPhotosCount];
            self.processedPhotosCountLabel.text = [NSString stringWithFormat:@"%ld", (long)processedPhotosCount];
            
            // 更新照片总数标签
            self.totalCountLabel.text = [NSString stringWithFormat:@"%ld 总数", (long)totalPhotosCount];
            
            // 更新进度条
            [self updateProgressBar:progressPercent];

            // 更新会员卡片显示状态
            [self updateMembershipCardVisibility];

            // 添加数字动画效果
            [UIView animateWithDuration:0.3 animations:^{
                self.totalPhotosCountLabel.transform = CGAffineTransformMakeScale(1.05, 1.05);
                self.deletedPhotosCountLabel.transform = CGAffineTransformMakeScale(1.05, 1.05);
                self.processedPhotosCountLabel.transform = CGAffineTransformMakeScale(1.05, 1.05);
            } completion:^(BOOL finished) {
                [UIView animateWithDuration:0.2 animations:^{
                    self.totalPhotosCountLabel.transform = CGAffineTransformIdentity;
                    self.deletedPhotosCountLabel.transform = CGAffineTransformIdentity;
                    self.processedPhotosCountLabel.transform = CGAffineTransformIdentity;
                }];
            }];
        });
    });
}

- (void)updateProgressBar:(CGFloat)percent {
    // 更新百分比标签
    self.progressPercentLabel.text = [NSString stringWithFormat:@"%.1f%%", percent];
    
    // 计算进度条宽度
    CGFloat progressBarBackgroundWidth = CGRectGetWidth(self.view.frame) - 80; // 减去左右边距
    CGFloat targetWidth = progressBarBackgroundWidth * (percent / 100.0);
    
    // 动画更新进度条宽度
    [UIView animateWithDuration:0.8 delay:0.2 options:UIViewAnimationOptionCurveEaseInOut animations:^{
        self.progressBarWidthConstraint.constant = targetWidth;
        [self.progressBarBackground layoutIfNeeded];
    } completion:nil];
    
    // 根据进度设置进度条颜色
    UIColor *progressColor;
    if (percent < 25) {
        progressColor = [UIColor systemRedColor];
    } else if (percent < 50) {
        progressColor = [UIColor systemOrangeColor];
    } else if (percent < 75) {
        progressColor = [UIColor systemYellowColor];
    } else {
        progressColor = [UIColor systemGreenColor];
    }
    
    [UIView animateWithDuration:0.3 animations:^{
        self.progressBarFill.backgroundColor = progressColor;
    }];
}



/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
