//
//  OnboardingPageViewController.m
//  photoclear
//
//  Created by lifubing on 2025/9/29.
//

#import "OnboardingPageViewController.h"

@interface OnboardingPageViewController ()

@property (nonatomic, strong) UIView *gradientView;
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
@property (nonatomic, strong) NSArray<CAGradientLayer *> *animatingLayers;
@property (nonatomic, strong) CADisplayLink *displayLink;
@property (nonatomic, assign) CFTimeInterval startTime;
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subtitleLabel;
@property (nonatomic, strong) UILabel *descriptionLabel;

@end

@implementation OnboardingPageViewController

- (instancetype)initWithPageType:(OnboardingPageType)pageType {
    self = [super init];
    if (self) {
        _pageType = pageType;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self configureForPageType];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    self.gradientLayer.frame = self.view.bounds;

    // 更新所有动画层的frame
    for (CAGradientLayer *layer in self.animatingLayers) {
        layer.frame = self.view.bounds;
    }
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self startLiquidAnimation];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    [self stopLiquidAnimation];
}

- (void)dealloc {
    [self stopLiquidAnimation];
}

- (void)setupUI {
    // 创建渐变背景
    self.gradientView = [[UIView alloc] init];
    [self.view addSubview:self.gradientView];

    // 创建主渐变层
    self.gradientLayer = [CAGradientLayer layer];
//    [self.gradientView.layer addSublayer:self.gradientLayer];

    // 创建多个动画渐变层实现液体效果
    [self setupLiquidLayers];

    // 创建Logo标签（替代图标）
    self.iconImageView = [[UIImageView alloc] init];
    self.iconImageView.contentMode = UIViewContentModeScaleAspectFit;
//    self.iconImageView.backgroundColor = [UIColor whiteColor];
    [self.iconImageView setImage:[UIImage imageNamed:@"icon_40x40"]];
    self.iconImageView.layer.cornerRadius = 8;
    self.iconImageView.clipsToBounds = YES;
    [self.view addSubview:self.iconImageView];

    // 创建主标题 - 更大的字体
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.textAlignment = NSTextAlignmentLeft;
    self.titleLabel.textColor = [UIColor whiteColor];
    self.titleLabel.font = [UIFont boldSystemFontOfSize:32];
    self.titleLabel.numberOfLines = 0;
    [self.view addSubview:self.titleLabel];

    // 创建副标题 - 更大的字体
    self.subtitleLabel = [[UILabel alloc] init];
    self.subtitleLabel.textAlignment = NSTextAlignmentLeft;
    self.subtitleLabel.textColor = [UIColor whiteColor];
    self.subtitleLabel.font = [UIFont boldSystemFontOfSize:32];
    self.subtitleLabel.numberOfLines = 0;
    [self.view addSubview:self.subtitleLabel];

    // 创建描述文本 - 更小更淡的字体
    self.descriptionLabel = [[UILabel alloc] init];
    self.descriptionLabel.textAlignment = NSTextAlignmentLeft;
    self.descriptionLabel.textColor = [UIColor colorWithWhite:1.0 alpha:0.7];
    self.descriptionLabel.font = [UIFont systemFontOfSize:14];
    self.descriptionLabel.numberOfLines = 0;
    [self.view addSubview:self.descriptionLabel];

    [self setupConstraints];
}

- (void)setupConstraints {
    self.gradientView.translatesAutoresizingMaskIntoConstraints = NO;
    self.iconImageView.translatesAutoresizingMaskIntoConstraints = NO;
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.subtitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.descriptionLabel.translatesAutoresizingMaskIntoConstraints = NO;
    
    [NSLayoutConstraint activateConstraints:@[
        // 渐变背景
        [self.gradientView.topAnchor constraintEqualToAnchor:self.view.topAnchor],
        [self.gradientView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.gradientView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.gradientView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor],

        // Logo
        [self.iconImageView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:32+2],
        [self.iconImageView.bottomAnchor constraintEqualToAnchor:self.titleLabel.topAnchor constant:-22],
        
        [self.iconImageView.widthAnchor constraintEqualToConstant:40],
        [self.iconImageView.heightAnchor constraintEqualToConstant:40],

        // 标题
        [self.titleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:32],
        [self.titleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-32],
//        [self.titleLabel.centerYAnchor constraintEqualToAnchor:self.view.centerYAnchor constant:-60],
        [self.titleLabel.bottomAnchor constraintEqualToAnchor:self.subtitleLabel.topAnchor constant:-8],
        
        // 副标题
        [self.subtitleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:32],
        [self.subtitleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-32],
//        [self.subtitleLabel.topAnchor constraintEqualToAnchor:self.titleLabel.bottomAnchor constant:4],
        [self.subtitleLabel.bottomAnchor constraintEqualToAnchor:self.descriptionLabel.topAnchor constant:-12],
        
        // 描述
        [self.descriptionLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:32],
        [self.descriptionLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-32],
        [self.descriptionLabel.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor constant:-50-30-50-44],
        
    ]];
}

- (void)configureForPageType {
    switch (self.pageType) {
        case OnboardingPageTypeWidget:
            [self configureWidgetPage];
            break;
        case OnboardingPageTypeAutoPlay:
            [self configureAutoPlayPage];
            break;
        case OnboardingPageTypeNoAds:
            [self configureNoAdsPage];
            break;
        case OnboardingPageTypePermission:
            [self configurePermissionPage];
            break;
    }
}

- (void)configureWidgetPage {
    // 蓝色渐变背景 - 精确匹配视觉稿颜色
    self.gradientLayer.colors = @[
        (id)[UIColor colorWithRed:0.2 green:0.5 blue:1.0 alpha:1.0].CGColor,  // 顶部亮蓝色
        (id)[UIColor colorWithRed:0.0 green:0.1 blue:0.4 alpha:1.0].CGColor   // 底部深蓝色
    ];
    self.gradientLayer.startPoint = CGPointMake(0.5, 0);
    self.gradientLayer.endPoint = CGPointMake(0.5, 1);

    // 设置液体动画层的颜色
    [self updateLiquidColorsForPageType];

    // Widget功能介绍文案
    self.titleLabel.text = @"Widget整理照片，";
    self.subtitleLabel.text = @"快捷高效无感。";
    self.descriptionLabel.text = @"创新推出Widget整理照片功能，实现在桌面上快速整理照片。利用碎片化时间，轻松处理相册照片";
}

- (void)configureAutoPlayPage {
    // 橙色渐变背景 - 精确匹配视觉稿颜色
    self.gradientLayer.colors = @[
        (id)[UIColor colorWithRed:1.0 green:0.6 blue:0.2 alpha:1.0].CGColor,  // 顶部橙色
        (id)[UIColor colorWithRed:0.6 green:0.2 blue:0.0 alpha:1.0].CGColor   // 底部深橙红色
    ];
    self.gradientLayer.startPoint = CGPointMake(0.5, 0);
    self.gradientLayer.endPoint = CGPointMake(0.5, 1);

    // 设置液体动画层的颜色
    [self updateLiquidColorsForPageType];

    // 自动轮播功能介绍文案
    self.titleLabel.text = @"APP照片整理，";
    self.subtitleLabel.text = @"自动轮播，无需繁琐的滑动。";
    self.descriptionLabel.text = @"照片自动轮播，只需要轻轻一点即可将照片进行整理\n让管理变得简单高效";
}

- (void)configureNoAdsPage {
    // 绿色渐变背景 - 精确匹配视觉稿颜色
    self.gradientLayer.colors = @[
        (id)[UIColor colorWithRed:0.5 green:0.9 blue:0.3 alpha:1.0].CGColor,  // 顶部亮绿色
        (id)[UIColor colorWithRed:0.1 green:0.4 blue:0.0 alpha:1.0].CGColor   // 底部深绿色
    ];
    self.gradientLayer.startPoint = CGPointMake(0.5, 0);
    self.gradientLayer.endPoint = CGPointMake(0.5, 1);

    // 设置液体动画层的颜色
    [self updateLiquidColorsForPageType];

    // 无广告功能介绍文案
    self.titleLabel.text = @"简洁无广,";
    self.subtitleLabel.text = @"专注您的照片整理体验.";
//    self.descriptionLabel.text = @"APP完全无广告，专注于为您提供纯净的照片整理体验\n没有任何干扰";
    self.descriptionLabel.text = @"您将享受无广告打扰的纯净环境，专心整理您的照片\n彻底摆脱广告的烦恼，轻松体验更高效的整理过程";
}

- (void)configurePermissionPage {
    // 紫色渐变背景 - 保持原有颜色
    self.gradientLayer.colors = @[
        (id)[UIColor colorWithRed:0.6 green:0.3 blue:1.0 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.4 green:0.1 blue:0.8 alpha:1.0].CGColor
    ];
    self.gradientLayer.startPoint = CGPointMake(0.5, 0);
    self.gradientLayer.endPoint = CGPointMake(0.5, 1);

    // 设置液体动画层的颜色
    [self updateLiquidColorsForPageType];

    // 权限申请页面文案
    self.titleLabel.text = @"访问您的照片";
    self.subtitleLabel.text = @"开始整理之旅";
    self.descriptionLabel.text = @"为了帮您整理照片，我们需要访问您的相册\n您的隐私安全是我们的首要考虑";
}

#pragma mark - Liquid Animation

- (void)setupLiquidLayers {
    NSMutableArray *layers = [NSMutableArray array];

    // 创建3-4个不同的渐变层，用于模拟液体粒子效果
    for (int i = 0; i < 4; i++) {
        CAGradientLayer *layer = [CAGradientLayer layer];
        layer.frame = self.view.bounds;
        layer.opacity = 0.3 + (i * 0.1); // 不同的透明度

        // 设置不同的渐变方向和位置
        CGFloat angle = (M_PI * 2 * i) / 4.0; // 每层不同角度
        layer.startPoint = CGPointMake(0.5 + 0.3 * cos(angle), 0.5 + 0.3 * sin(angle));
        layer.endPoint = CGPointMake(0.5 - 0.3 * cos(angle), 0.5 - 0.3 * sin(angle));

        [self.gradientView.layer insertSublayer:layer below:self.gradientLayer];
        [layers addObject:layer];
    }

    self.animatingLayers = [layers copy];
}

- (void)startLiquidAnimation {
    if (self.displayLink) {
        [self stopLiquidAnimation];
    }

    self.startTime = CACurrentMediaTime();
    self.displayLink = [CADisplayLink displayLinkWithTarget:self selector:@selector(updateLiquidAnimation:)];
    [self.displayLink addToRunLoop:[NSRunLoop mainRunLoop] forMode:NSRunLoopCommonModes];
}

- (void)stopLiquidAnimation {
    if (self.displayLink) {
        [self.displayLink invalidate];
        self.displayLink = nil;
    }
}

- (void)updateLiquidAnimation:(CADisplayLink *)displayLink {
    CFTimeInterval elapsed = CACurrentMediaTime() - self.startTime;

    // 为每个动画层创建不同的动画参数
    for (int i = 0; i < self.animatingLayers.count; i++) {
        CAGradientLayer *layer = self.animatingLayers[i];

        // 使用固定的周期性动画，避免速度累积
        CGFloat baseSpeed = 0.8; // 基础动画速度
        CGFloat layerSpeedMultiplier = 1.0 + (i * 0.3); // 每层的速度倍数
        CGFloat phase = (M_PI * 2 * i) / self.animatingLayers.count; // 不同的相位

        // 计算当前动画时间，使用模运算保持周期性
        CGFloat animationTime = fmod(elapsed * baseSpeed * layerSpeedMultiplier, M_PI * 4); // 4π为一个完整周期

        // 动态改变渐变的起始和结束点，使用更平缓的变化
        CGFloat radiusX = 0.25 + 0.15 * sin(animationTime * 0.5 + phase);
        CGFloat radiusY = 0.25 + 0.15 * cos(animationTime * 0.7 + phase * 0.8);
        CGFloat angle = animationTime * 0.4 + phase;

        // 限制渐变点在合理范围内
        CGFloat startX = 0.5 + radiusX * cos(angle);
        CGFloat startY = 0.5 + radiusY * sin(angle);
        CGFloat endX = 0.5 - radiusX * cos(angle);
        CGFloat endY = 0.5 - radiusY * sin(angle);

        // 确保点在[0,1]范围内
        startX = MAX(0.1, MIN(0.9, startX));
        startY = MAX(0.1, MIN(0.9, startY));
        endX = MAX(0.1, MIN(0.9, endX));
        endY = MAX(0.1, MIN(0.9, endY));

        layer.startPoint = CGPointMake(startX, startY);
        layer.endPoint = CGPointMake(endX, endY);

        // 更平缓的透明度变化
        CGFloat opacityBase = 0.15 + (i * 0.05); // 每层不同的基础透明度
        CGFloat opacityVariation = 0.15 * (0.5 + 0.5 * sin(animationTime * 0.6 + phase));
        layer.opacity = opacityBase + opacityVariation;
    }
}

- (void)updateLiquidColorsForPageType {
    NSArray *baseColors = [self getBaseColorsForCurrentPage];

    if (baseColors.count >= 2) {
        UIColor *color1 = baseColors[0];
        UIColor *color2 = baseColors[1];

        // 为每个动画层设置略微不同的颜色变化
        for (int i = 0; i < self.animatingLayers.count; i++) {
            CAGradientLayer *layer = self.animatingLayers[i];

            // 创建颜色变化
            CGFloat hueShift = (i * 0.05); // 轻微的色相偏移
            CGFloat saturationMultiplier = 0.8 + (i * 0.1); // 饱和度变化

            UIColor *adjustedColor1 = [self adjustColor:color1 hueShift:hueShift saturationMultiplier:saturationMultiplier];
            UIColor *adjustedColor2 = [self adjustColor:color2 hueShift:hueShift saturationMultiplier:saturationMultiplier];

            layer.colors = @[
                (id)adjustedColor1.CGColor,
                (id)adjustedColor2.CGColor
            ];
        }
    }
}

- (NSArray<UIColor *> *)getBaseColorsForCurrentPage {
    switch (self.pageType) {
        case OnboardingPageTypeWidget:
            return @[
                [UIColor whiteColor],
                [UIColor blackColor]
            ];
        case OnboardingPageTypeAutoPlay:
            return @[
                [UIColor colorWithRed:1.0 green:0.6 blue:0.2 alpha:1.0],
                [UIColor colorWithRed:0.6 green:0.2 blue:0.0 alpha:1.0]
            ];
        case OnboardingPageTypeNoAds:
            return @[
                [UIColor colorWithRed:0.5 green:0.9 blue:0.3 alpha:1.0],
                [UIColor colorWithRed:0.1 green:0.4 blue:0.0 alpha:1.0]
            ];
        case OnboardingPageTypePermission:
            return @[
                [UIColor colorWithRed:0.6 green:0.3 blue:1.0 alpha:1.0],
                [UIColor colorWithRed:0.4 green:0.1 blue:0.8 alpha:1.0]
            ];
        default:
            return @[
                [UIColor colorWithRed:0.2 green:0.5 blue:1.0 alpha:1.0],
                [UIColor colorWithRed:0.0 green:0.1 blue:0.4 alpha:1.0]
            ];
    }
}

- (UIColor *)adjustColor:(UIColor *)color hueShift:(CGFloat)hueShift saturationMultiplier:(CGFloat)saturationMultiplier {
    CGFloat hue, saturation, brightness, alpha;
    if ([color getHue:&hue saturation:&saturation brightness:&brightness alpha:&alpha]) {
        hue = fmod(hue + hueShift, 1.0);
        saturation = MIN(1.0, saturation * saturationMultiplier);
        return [UIColor colorWithHue:hue saturation:saturation brightness:brightness alpha:alpha];
    }
    return color;
}

@end
