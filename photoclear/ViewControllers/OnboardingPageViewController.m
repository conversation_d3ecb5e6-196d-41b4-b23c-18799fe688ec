//
//  OnboardingPageViewController.m
//  photoclear
//
//  Created by lifubing on 2025/9/29.
//

#import "OnboardingPageViewController.h"

@interface OnboardingPageViewController ()

@property (nonatomic, strong) UIView *gradientView;
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subtitleLabel;
@property (nonatomic, strong) UILabel *descriptionLabel;

@end

@implementation OnboardingPageViewController

- (instancetype)initWithPageType:(OnboardingPageType)pageType {
    self = [super init];
    if (self) {
        _pageType = pageType;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self configureForPageType];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    self.gradientLayer.frame = self.view.bounds;
}

- (void)setupUI {
    // 创建渐变背景
    self.gradientView = [[UIView alloc] init];
    [self.view addSubview:self.gradientView];

    self.gradientLayer = [CAGradientLayer layer];
    [self.gradientView.layer addSublayer:self.gradientLayer];

    // 创建Logo标签（替代图标）
    self.iconImageView = [[UIImageView alloc] init];
    self.iconImageView.contentMode = UIViewContentModeScaleAspectFit;
//    self.iconImageView.backgroundColor = [UIColor whiteColor];
    [self.iconImageView setImage:[UIImage imageNamed:@"icon_40x40"]];
    self.iconImageView.layer.cornerRadius = 8;
    self.iconImageView.clipsToBounds = YES;
    [self.view addSubview:self.iconImageView];

    // 创建主标题 - 更大的字体
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.textAlignment = NSTextAlignmentLeft;
    self.titleLabel.textColor = [UIColor whiteColor];
    self.titleLabel.font = [UIFont boldSystemFontOfSize:32];
    self.titleLabel.numberOfLines = 0;
    [self.view addSubview:self.titleLabel];

    // 创建副标题 - 更大的字体
    self.subtitleLabel = [[UILabel alloc] init];
    self.subtitleLabel.textAlignment = NSTextAlignmentLeft;
    self.subtitleLabel.textColor = [UIColor whiteColor];
    self.subtitleLabel.font = [UIFont boldSystemFontOfSize:32];
    self.subtitleLabel.numberOfLines = 0;
    [self.view addSubview:self.subtitleLabel];

    // 创建描述文本 - 更小更淡的字体
    self.descriptionLabel = [[UILabel alloc] init];
    self.descriptionLabel.textAlignment = NSTextAlignmentLeft;
    self.descriptionLabel.textColor = [UIColor colorWithWhite:1.0 alpha:0.7];
    self.descriptionLabel.font = [UIFont systemFontOfSize:14];
    self.descriptionLabel.numberOfLines = 0;
    [self.view addSubview:self.descriptionLabel];

    [self setupConstraints];
}

- (void)setupConstraints {
    self.gradientView.translatesAutoresizingMaskIntoConstraints = NO;
    self.iconImageView.translatesAutoresizingMaskIntoConstraints = NO;
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.subtitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.descriptionLabel.translatesAutoresizingMaskIntoConstraints = NO;
    
    [NSLayoutConstraint activateConstraints:@[
        // 渐变背景
        [self.gradientView.topAnchor constraintEqualToAnchor:self.view.topAnchor],
        [self.gradientView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.gradientView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.gradientView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor],

        // Logo
        [self.iconImageView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:32+2],
        [self.iconImageView.bottomAnchor constraintEqualToAnchor:self.titleLabel.topAnchor constant:-22],
        
        [self.iconImageView.widthAnchor constraintEqualToConstant:40],
        [self.iconImageView.heightAnchor constraintEqualToConstant:40],

        // 标题
        [self.titleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:32],
        [self.titleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-32],
//        [self.titleLabel.centerYAnchor constraintEqualToAnchor:self.view.centerYAnchor constant:-60],
        [self.titleLabel.bottomAnchor constraintEqualToAnchor:self.subtitleLabel.topAnchor constant:-8],
        
        // 副标题
        [self.subtitleLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:32],
        [self.subtitleLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-32],
//        [self.subtitleLabel.topAnchor constraintEqualToAnchor:self.titleLabel.bottomAnchor constant:4],
        [self.subtitleLabel.bottomAnchor constraintEqualToAnchor:self.descriptionLabel.topAnchor constant:-12],
        
        // 描述
        [self.descriptionLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:32],
        [self.descriptionLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-32],
        [self.descriptionLabel.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor constant:-50-30-50-44],
        
    ]];
}

- (void)configureForPageType {
    switch (self.pageType) {
        case OnboardingPageTypeWidget:
            [self configureWidgetPage];
            break;
        case OnboardingPageTypeAutoPlay:
            [self configureAutoPlayPage];
            break;
        case OnboardingPageTypeNoAds:
            [self configureNoAdsPage];
            break;
        case OnboardingPageTypePermission:
            [self configurePermissionPage];
            break;
    }
}

- (void)configureWidgetPage {
    // 蓝色渐变背景 - 精确匹配视觉稿颜色
    self.gradientLayer.colors = @[
        (id)[UIColor colorWithRed:0.2 green:0.5 blue:1.0 alpha:1.0].CGColor,  // 顶部亮蓝色
        (id)[UIColor colorWithRed:0.0 green:0.1 blue:0.4 alpha:1.0].CGColor   // 底部深蓝色
    ];
    self.gradientLayer.startPoint = CGPointMake(0.5, 0);
    self.gradientLayer.endPoint = CGPointMake(0.5, 1);

    // Widget功能介绍文案
    self.titleLabel.text = @"Widget整理照片，";
    self.subtitleLabel.text = @"快捷高效无感。";
    self.descriptionLabel.text = @"创新推出Widget整理照片功能，实现在桌面上快速整理照片。利用碎片化时间，轻松处理相册照片";
}

- (void)configureAutoPlayPage {
    // 橙色渐变背景 - 精确匹配视觉稿颜色
    self.gradientLayer.colors = @[
        (id)[UIColor colorWithRed:1.0 green:0.6 blue:0.2 alpha:1.0].CGColor,  // 顶部橙色
        (id)[UIColor colorWithRed:0.6 green:0.2 blue:0.0 alpha:1.0].CGColor   // 底部深橙红色
    ];
    self.gradientLayer.startPoint = CGPointMake(0.5, 0);
    self.gradientLayer.endPoint = CGPointMake(0.5, 1);

    // 自动轮播功能介绍文案
    self.titleLabel.text = @"APP照片整理，";
    self.subtitleLabel.text = @"自动轮播，无需繁琐的滑动。";
    self.descriptionLabel.text = @"照片自动轮播，只需要轻轻一点即可将照片进行整理\n让管理变得简单高效";
}

- (void)configureNoAdsPage {
    // 绿色渐变背景 - 精确匹配视觉稿颜色
    self.gradientLayer.colors = @[
        (id)[UIColor colorWithRed:0.5 green:0.9 blue:0.3 alpha:1.0].CGColor,  // 顶部亮绿色
        (id)[UIColor colorWithRed:0.1 green:0.4 blue:0.0 alpha:1.0].CGColor   // 底部深绿色
    ];
    self.gradientLayer.startPoint = CGPointMake(0.5, 0);
    self.gradientLayer.endPoint = CGPointMake(0.5, 1);


    // 无广告功能介绍文案
    self.titleLabel.text = @"简洁无广,";
    self.subtitleLabel.text = @"专注您的照片整理体验.";
//    self.descriptionLabel.text = @"APP完全无广告，专注于为您提供纯净的照片整理体验\n没有任何干扰";
    self.descriptionLabel.text = @"您将享受无广告打扰的纯净环境，专心整理您的照片\n彻底摆脱广告的烦恼，轻松体验更高效的整理过程";
}

- (void)configurePermissionPage {
    // 紫色渐变背景 - 保持原有颜色
    self.gradientLayer.colors = @[
        (id)[UIColor colorWithRed:0.6 green:0.3 blue:1.0 alpha:1.0].CGColor,
        (id)[UIColor colorWithRed:0.4 green:0.1 blue:0.8 alpha:1.0].CGColor
    ];
    self.gradientLayer.startPoint = CGPointMake(0.5, 0);
    self.gradientLayer.endPoint = CGPointMake(0.5, 1);

    // 权限申请页面文案
    self.titleLabel.text = @"访问您的照片";
    self.subtitleLabel.text = @"开始整理之旅";
    self.descriptionLabel.text = @"为了帮您整理照片，我们需要访问您的相册\n您的隐私安全是我们的首要考虑";
}

@end
