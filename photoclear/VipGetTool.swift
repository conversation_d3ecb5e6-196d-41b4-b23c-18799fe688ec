//
//  VipGetTool.swift
//  wordWidget
//
//  Created by lifubing on 2024/8/22.
//

import Foundation
import SwiftyStoreKit

// 定义通知的名称
extension Notification.Name {
    static let VipChangeNotification = Notification.Name("VipChangeNotification")
}

let productID0:String = "com.lfb.photoclear.yearpay"
let productID1:String = "com.lfb.photoclear.lifetime"

class VipGetToolManager:NSObject {
    
    // 创建单例实例
    @objc public static let shared = VipGetToolManager()
    // 1、支付
    @objc public func purchaseProduct(index:Int) {
        var productID = productID0
        if index == 1 {
            productID = productID1
        }
        
        SwiftyStoreKit.purchaseProduct(productID, atomically: true) { result in
            switch result {
                
            case .success(let purchase):
                AlertManager.shared.showLoading("购买成功，正在校验..")
                print("购买成功: \(purchase.productId)")
                Preferences.sharedInstance().productID = purchase.productId as NSString
                
                self.verifyReceipt(purchase.transaction, true)
                
            case .error(let error):
                var alertTips = ""
                switch error.code {
                case .unknown:
                    alertTips = "未知错误，请联系作者支持"
                case .clientInvalid:
                    alertTips = "不允许进行支付"
                case .paymentCancelled:
                    alertTips = "支付已取消"
                case .paymentInvalid:
                    alertTips = "购买标识无效"
                case .paymentNotAllowed:
                    alertTips = "设备不允许进行支付"
                case .storeProductNotAvailable:
                    alertTips = "产品在当前商店中不可用"
                case .cloudServicePermissionDenied:
                    alertTips = "访问云服务信息被拒绝"
                case .cloudServiceNetworkConnectionFailed:
                    alertTips = "无法连接到网络"
                case .cloudServiceRevoked:
                    alertTips = "用户已撤销使用云服务的权限"
                default:
                    alertTips = "错误详情: \(error.localizedDescription)"
                }
                AlertManager.shared.showTips(alertTips)
            case .deferred(purchase: _):
                AlertManager.shared.showTips("未知错误，请联系作者支持")
            }
        }
    }
    
    // 2、验证
    func verifyReceipt(_ transaction: PaymentTransaction? = nil, _ needshowTips:Bool = false) {
        // 验证收据并检查订阅状态

        SwiftyStoreKit.verifyReceipt(using: AppleReceiptValidator(service: .production, sharedSecret: "8fb4ebf15fbd48bd9222d144e88c3a38"), forceRefresh: false) { result in
            switch result {
            case .success(let receipt):
                self.checkSubscriptionStatus(receipt: receipt,transaction:transaction, needshowTips)
            case .error(let error):
                print("收据验证失败: \(error.localizedDescription)")
                
//                UserDefaultsManager.shared.save(value: "0", for: .vipGet)
                Preferences.sharedInstance().didVipGet = false
                if needshowTips {
                    AlertManager.shared.showTips("验证失败，请换个网络重试，或者联系作者。")
                }
            }
        }
    }
    
    @objc public func verifyReceipt() {
        SwiftyStoreKit.verifyReceipt(using: AppleReceiptValidator(service: .production, sharedSecret: "8fb4ebf15fbd48bd9222d144e88c3a38"), forceRefresh: false) { result in
            switch result {
            case .success(let receipt):
                self.checkSubscriptionStatus(receipt: receipt,transaction:nil, false)
            case .error(let error):
                print("收据验证失败: \(error.localizedDescription)")
                Preferences.sharedInstance().didVipGet = false
            }
        }
    }
    
    // 3、恢复购买
    func restorePurchases() {
        SwiftyStoreKit.restorePurchases(atomically: true) { results in
            if results.restoreFailedPurchases.count > 0 {
                print("恢复失败: \(results.restoreFailedPurchases)")
                AlertManager.shared.showTips("恢复购买失败")
            } else if results.restoredPurchases.count > 0 {
                for purchase in results.restoredPurchases {
                    AlertManager.shared.showLoading("恢复购买成功，验证中")
                    // 如果需要，手动完成交易
                    self.verifyReceipt(purchase.transaction, true)
                }
            } else {
                AlertManager.shared.showTips("还未购买此订阅。")
            }
        }
    }
    
    fileprivate func checkSubscriptionStatus(receipt: ReceiptInfo, transaction: PaymentTransaction? , _ needshowTips:Bool = false) {
        // 验证订阅状态
        let product_ID = String(Preferences.sharedInstance().productID)
        if product_ID == productID0 {
            // 订阅
            let result = SwiftyStoreKit.verifySubscription(ofType: .autoRenewable, productId: product_ID, inReceipt: receipt, validUntil: Date())
            
            switch result {
             case .purchased(let expiryDate, _):
                if let trans = transaction {
                    SwiftyStoreKit.finishTransaction(trans)
                }
                if needshowTips {
                    showAlert("购买成功，有效至 \(expiryDate)")
                }
                Preferences.sharedInstance().didVipGet = true
                NotificationCenter.default.post(name: .VipChangeNotification, object: nil)
             case .expired(let expiryDate, _):
                if needshowTips {
                    showAlert("订阅已过期，过期时间: \(expiryDate)")
                }
                Preferences.sharedInstance().didVipGet = false
             case .notPurchased:
                if needshowTips {
                    showAlert("未购买此订阅")
                }
                Preferences.sharedInstance().didVipGet = false
             }
        } else {
            // 内购
            let result = SwiftyStoreKit.verifyPurchase(productId: product_ID, inReceipt: receipt)
            switch result {
            case .purchased(let item):
                if let trans = transaction {
                    SwiftyStoreKit.finishTransaction(trans)
                }
                if needshowTips {
                    showAlert("恭喜您，终身会员购买成功！")
                }
                Preferences.sharedInstance().didVipGet = true
                NotificationCenter.default.post(name: .VipChangeNotification, object: nil)
            default:
                if needshowTips {
                    showAlert("此会员未完成购买")
                }
                Preferences.sharedInstance().didVipGet = false
            }
        }
    }
    
    func showAlert(_ str:String) {
        NSObject.cancelPreviousPerformRequests(withTarget: self)
        self.perform(#selector(doShowAlert), with: str, afterDelay: 1)
    }
    
    @objc func doShowAlert(_ str:String) {
        AlertManager.shared.showTips(str)
    }
    
    @objc func completeTransactions() {
        SwiftyStoreKit.completeTransactions(atomically: true) { purchases in
            for purchase in purchases {
                switch purchase.transaction.transactionState {
                case .purchased, .restored:
                    if purchase.needsFinishTransaction {
                        VipGetToolManager.shared.verifyReceipt(purchase.transaction)
                    }
                case .failed, .purchasing, .deferred:
                    break // do nothing
                @unknown default:
                    break
                }
            }
        }
    }
}
